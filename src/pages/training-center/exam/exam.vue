<script setup lang="ts">
import { computed, ref, onUnmounted } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import { getAnswerDetail, submitAnswer as apiSubmitAnswer, updateAnswerProgress } from '@/api/exam'
import type { ExamAnswerDetail, ExamQuestion, ExamQuestionOption } from '@/types/api/Exam'
import { useToast } from 'wot-design-uni'
import dayjs from 'dayjs'

const answerId = ref<number | null>(null)
const examDetail = ref<ExamAnswerDetail | null>(null)
const currentQuestionIndex = ref(0)
const userAnswers = ref<Record<string, any>>({})
const remainingTime = ref('00:00:00')
const isLoading = ref(true)
const isSubmitted = ref(false)
const toast = useToast()
let timer: any = null

onLoad(async (options) => {
  if (options?.answerId) {
    const id = parseInt(options.answerId, 10)
    answerId.value = id
    await fetchExamDetail(id)
  }
})

onUnmounted(() => {
  if (timer) {
    clearInterval(timer)
  }
})

async function fetchExamDetail(id: number) {
  isLoading.value = true
  try {
    const data = await getAnswerDetail({ answerId: id })
    console.log('获取到的考试数据:', data)
    console.log('题目数量:', data.questions?.length)
    if (data.questions && data.questions.length > 0) {
      console.log('第一题选项:', data.questions[0].options)
    }
    examDetail.value = data
    if (data.answers) {
      userAnswers.value = data.answers
    }
    setupTimer(data.startTime, data.duration)
  }
  catch (error) {
    console.error('获取考试详情失败:', error)
    toast.error('获取考试详情失败')
  }
  finally {
    isLoading.value = false
  }
}

function setupTimer(startTimeStr: string, durationMinutes: number) {
  if (timer) {
    clearInterval(timer)
  }
  const startTime = dayjs(startTimeStr)
  const endTime = startTime.add(durationMinutes, 'minute')

  timer = setInterval(async () => {
    const now = dayjs()
    if (now.isAfter(endTime)) {
      if (timer)
        clearInterval(timer)
      remainingTime.value = '00:00:00'
      // 注释自动提交功能
      // await submitAnswer(true)
      return
    }
    const diff = endTime.diff(now)
    const hours = Math.floor(diff / (1000 * 60 * 60))
    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60))
    const seconds = Math.floor((diff % (1000 * 60)) / 1000)
    remainingTime.value = [hours, minutes, seconds]
      .map(v => v.toString().padStart(2, '0'))
      .join(':')
  }, 1000)
}

const currentQuestion = computed<ExamQuestion | null>(() => {
  return examDetail.value?.questions[currentQuestionIndex.value] ?? null
})

const totalQuestions = computed(() => examDetail.value?.questions.length ?? 0)

const progress = computed(() => {
  if (totalQuestions.value === 0)
    return 0
  return ((currentQuestionIndex.value + 1) / totalQuestions.value) * 100
})

const questionTypeMap: Record<string, string> = {
  SINGLE: '单选题',
  MULTIPLE: '多选题',
}

const questionTypeText = computed(() => {
  return currentQuestion.value?.type ? questionTypeMap[currentQuestion.value.type] : ''
})

function selectOption(option: ExamQuestionOption) {
  if (isSubmitted.value || !currentQuestion.value?.id || !option.optionKey)
    return

  const questionId = currentQuestion.value.id
  const currentAnswer = userAnswers.value[questionId]

  if (currentQuestion.value.type === 'MULTIPLE') {
    const answers = Array.isArray(currentAnswer) ? [...currentAnswer] : []
    const index = answers.indexOf(option.optionKey)
    if (index > -1) {
      answers.splice(index, 1)
    }
    else {
      answers.push(option.optionKey)
    }
    userAnswers.value[questionId] = answers.sort()
  }
  else {
    userAnswers.value[questionId] = option.optionKey
  }
}

function isOptionSelected(optionKey: string): boolean {
  if (!currentQuestion.value?.id)
    return false
  const answer = userAnswers.value[currentQuestion.value.id]
  if (Array.isArray(answer)) {
    return answer.includes(optionKey)
  }
  return answer === optionKey
}

// 格式化答案数据，将多选题数组转换为逗号分隔的字符串
function formatAnswerData(answers: Record<string, any>): Record<string, string> {
  const formattedAnswers: Record<string, string> = {}

  for (const [questionId, answer] of Object.entries(answers)) {
    if (Array.isArray(answer)) {
      // 多选题：将数组转换为逗号分隔的字符串
      formattedAnswers[questionId] = answer.join(',')
    } else if (answer !== undefined && answer !== null) {
      // 单选题：直接转换为字符串
      formattedAnswers[questionId] = String(answer)
    }
  }

  return formattedAnswers
}

async function submitAnswer(isAutoSubmit = false) {
  if (!answerId.value)
    return
  try {
    const formattedAnswers = formatAnswerData(userAnswers.value)
    console.log('提交答案 - 原始数据:', userAnswers.value)
    console.log('提交答案 - 格式化后:', formattedAnswers)
    await apiSubmitAnswer({ id: answerId.value, answerData: formattedAnswers })
    isSubmitted.value = true
    if (!isAutoSubmit) {
      toast.success('提交成功')
    }
    else {
      toast.show('时间到，已自动交卷')
    }
  }
  catch (error) {
    toast.error('提交失败')
  }
}

async function nextQuestion() {
  if (!answerId.value)
    return

  if (currentQuestionIndex.value < totalQuestions.value - 1) {
    const formattedAnswers = formatAnswerData(userAnswers.value)
    await updateAnswerProgress({ id: answerId.value, answerData: formattedAnswers })
    currentQuestionIndex.value++
    isSubmitted.value = false
  }
  else {
    await submitAnswer()
  }
}

async function prevQuestion() {
  if (!answerId.value)
    return
  const formattedAnswers = formatAnswerData(userAnswers.value)
  await updateAnswerProgress({ id: answerId.value, answerData: formattedAnswers })
  if (currentQuestionIndex.value > 0) {
    currentQuestionIndex.value--
    isSubmitted.value = false
  }
}

function navigateToList() {
  uni.navigateTo({
    url: '/pages/training-center/exam/list',
  })
}

function getCorrectOptionText(question: ExamQuestion | null): string {
  if (!question || !question.answer || !question.options)
    return ''
  const correctOption = question.options.find(opt => opt.optionKey === question.answer)
  return correctOption?.content ?? ''
}
</script>

<template>
  <view class="exam-question-page">
    <wd-toast />
    <view v-if="isLoading" class="loading-container">
      <wd-loading size="24px" />
    </view>
    <view v-else-if="examDetail && currentQuestion">
      <view class="progress-section">
        <view class="progress-info">
          <view class="time-info">
            <view class="icon-clock">
              <svg xmlns="http://www.w3.org/2000/svg" width="10" height="10" viewBox="0 0 10 10" fill="none">
                <path d="M5.25391 0.83252L5.25391 0.421399C5.25391 0.207538 5.08008 0.0337158 4.86622 0.0337158C4.65235 0.0337158 4.47853 0.207538 4.47853 0.421399V0.851075C4.19922 0.881347 3.92578 0.937011 3.65039 1.02099C1.22852 1.75244 -0.146484 4.3178 -0.0751949 6.7396C-0.0126949 6.94467 0.203125 7.0599 0.408203 6.99838C0.613281 6.93685 0.729492 6.72006 0.666992 6.51499C0.0585941 4.50234 1.20117 2.37057 3.21387 1.76316C5.22656 1.15576 7.35832 2.29733 7.96573 4.30998C8.57413 6.32262 7.43156 8.45439 5.41891 9.06179C3.96875 9.50026 2.39653 9.03836 1.41414 7.88507C1.27547 7.72199 1.03035 7.70246 0.867267 7.84112C0.704181 7.97978 0.68465 8.22489 0.823312 8.38798C1.71099 9.42702 2.99414 9.99999 4.31734 9.99999C4.75977 9.99999 5.20703 9.93554 5.64355 9.80371C8.06535 9.07131 9.43938 6.50595 8.70793 4.08512C8.1386 2.19845 6.45598 0.947509 4.59473 0.832275V0.83252H5.25391Z" fill="#1677FF"/>
                <path d="M5.66097 2.1594C5.10337 1.27662 4.32312 0.546167 3.40615 0.047158C3.21768 -0.0553782 2.98234 0.0149323 2.88078 0.203404C2.77922 0.391875 2.84855 0.62722 3.03605 0.728779C3.83778 1.16431 4.51843 1.80297 5.00572 2.57443C5.07993 2.69161 5.20493 2.75509 5.33383 2.75509C5.40512 2.75509 5.47641 2.73556 5.54086 2.69552C5.72152 2.58029 5.7762 2.34104 5.66097 2.1594ZM0.387684 3.29023C0.173823 3.29023 0 3.46405 0 3.67791V5.39662C0.00292969 5.5011 0.04785 5.59485 0.117184 5.66223L0.889623 6.54209C0.965792 6.629 1.07321 6.67392 1.18063 6.67392C1.27145 6.67392 1.36227 6.6417 1.43648 6.57725C1.59761 6.43565 1.61324 6.19151 1.47164 6.03039L0.774392 5.23646V3.67791C0.775368 3.46405 0.601545 3.29023 0.387684 3.29023Z" fill="#1677FF"/>
              </svg>
            </view>
            <text class="text-label">剩余时间：</text>
            <text class="text-value primary">{{ remainingTime }}</text>
          </view>
          <view class="question-count">
            <text class="text-label">题目：</text>
            <text class="text-value primary">{{ currentQuestionIndex + 1 }}/{{ totalQuestions }}</text>
          </view>
        </view>
        <view class="progress-bar-container">
          <view class="progress-bar-track">
            <view class="progress-bar-fill" :style="{ width: progress + '%' }"></view>
          </view>
        </view>
      </view>

      <view class="question-card">
        <view class="question-header">
          <text>问题 {{ currentQuestionIndex + 1 }}：{{ questionTypeText }}</text>
        </view>
        <view class="question-content">
          <text>{{ currentQuestion.title }}</text>
        </view>
        <view class="options-container">
          <!-- 调试信息 -->
          <view v-if="!currentQuestion?.options || currentQuestion.options.length === 0" style="padding: 10px; color: red;">
            <text>调试信息：没有选项数据</text>
            <text>currentQuestion: {{ currentQuestion ? '存在' : '不存在' }}</text>
            <text>options: {{ currentQuestion?.options ? currentQuestion.options.length + '个选项' : '无选项' }}</text>
          </view>

          <view
            v-for="option in currentQuestion?.options || []"
            :key="option.id"
            class="option-item"
            :class="{ selected: isOptionSelected(option.optionKey!) }"
            @click="selectOption(option)"
          >
            <view class="option-indicator" :class="{ selected: isOptionSelected(option.optionKey!) }">
              <text>{{ option.optionKey }}</text>
            </view>
            <text class="option-text">{{ option.content }}</text>
          </view>
        </view>
        <!-- 注释提交答案按钮 -->
        <!-- <view class="submit-button-container">
          <view class="submit-button" @click="submitAnswer()">
            <text>提交答案</text>
          </view>
        </view> -->
        <view v-if="isSubmitted" class="explanation-section">
          <text class="explanation-title">答案解析</text>
          <text class="explanation-content">正确答案：{{ currentQuestion.answer }} - {{ getCorrectOptionText(currentQuestion) }}</text>
          <text class="explanation-detail">{{ currentQuestion.analysis }}</text>
        </view>
      </view>

      <view class="bottom-navigation">
        <view class="nav-button prev-button" @click="prevQuestion">
          <view class="icon-arrow-left">
            <svg xmlns="http://www.w3.org/2000/svg" width="8" height="14" viewBox="0 0 8 14" fill="none">
              <path d="M7.14197 13.7985C6.98751 13.7978 6.83961 13.7359 6.73072 13.6264L0.230721 7.1264C0.00454944 6.89888 0.00454944 6.53143 0.230721 6.3039L6.73072 0.203903C6.95181 -0.0542651 7.34592 -0.0694876 7.58626 0.170858C7.82661 0.411202 7.81139 0.805313 7.55322 1.0264L1.46739 4.11515L7.55322 7.2039C7.71902 7.37069 7.76845 7.62073 7.67859 7.83806C7.58872 8.05539 7.37714 8.1975 7.14197 8.19849L7.14197 13.7985Z" fill="#1677FF"/>
            </svg>
          </view>
          <text>上一题</text>
        </view>
        <view class="nav-button list-button" @click="navigateToList">
          <text>题目列表</text>
        </view>
        <view class="nav-button next-button" @click="nextQuestion">
          <text>{{ currentQuestionIndex === totalQuestions - 1 ? '交卷' : '下一题' }}</text>
          <view v-if="currentQuestionIndex < totalQuestions - 1" class="icon-arrow-right">
            <svg xmlns="http://www.w3.org/2000/svg" width="8" height="14" viewBox="0 0 8 14" fill="none">
              <path d="M0.858031 13.7985C1.01249 13.7978 1.16039 13.7359 1.26928 13.6264L7.76928 7.1264C7.99545 6.89888 7.99545 6.53143 7.76928 6.3039L1.26928 0.203903C1.04819 -0.0542651 0.654082 -0.0694876 0.413736 0.170858C0.173391 0.411202 0.188613 0.805313 0.446784 1.0264L6.53261 4.11515L0.446784 7.2039C0.280983 7.37069 0.231553 7.62073 0.321417 7.83806C0.411282 8.05539 0.622863 8.1975 0.858031 8.19849L0.858031 13.7985Z" fill="#FFFFFF"/>
            </svg>
          </view>
        </view>
      </view>
    </view>
    <view v-else class="error-container">
      <text>无法加载考试内容，请返回重试。</text>
    </view>
  </view>
</template>

<route lang="json">
{
  "style": {
    "navigationStyle": "default",
    "navigationBarTitleText": "单片更换培训考试"
  }
}
</route>

<style scoped lang="scss">
$page-bg: #f7f7f5;
$card-bg: #ffffff;
$primary-blue: #1677ff;
$light-blue-bg: #f0f7ff;
$button-blue-bg: #37acfe;
$text-dark: #333333;
$text-medium-dark: #4b4b4b;
$text-gray: #666666;
$border-gray-light: #e8e8e8;
$border-gray-medium: #d9d9d9;
$progress-bar-track-bg: #e6f4ff;
$prev-button-border: #d6e8ff;
$list-button-bg: #f5f7fa;
$list-button-border: #e0e0e0;
$text-white: #ffffff;

.exam-question-page {
  background-color: $page-bg;
  min-height: 100vh;
  padding-bottom: 95px;
}

.loading-container,
.error-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  color: $text-gray;
}

.progress-section {
  background-color: $card-bg;
  padding: 12px 18px;
  margin-bottom: 10px;
}

.progress-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 13px;
  color: $text-gray;
  margin-bottom: 10px;
}

.time-info,
.question-count {
  display: flex;
  align-items: center;
}

.icon-clock {
  width: 16px;
  height: 16px;
  margin-right: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  svg {
    width: 10px;
    height: 10px;
  }
}

.text-label {
  color: $text-gray;
}

.text-value.primary {
  color: $primary-blue;
  margin-left: 4px;
}

.progress-bar-container {
  height: 2px;
  background-color: $progress-bar-track-bg;
  border-radius: 1px;
}

.progress-bar-fill {
  height: 100%;
  background-color: $primary-blue;
  border-radius: 1px;
}

.question-card {
  background-color: $card-bg;
  margin: 0 16px;
  border-radius: 8px;
  padding-bottom: 20px;
}

.question-header {
  background-color: $light-blue-bg;
  padding: 8px 16px;
  border-top-left-radius: 8px;
  border-top-right-radius: 8px;
  font-size: 15px;
  color: $primary-blue;
}

.question-content {
  padding: 15px 16px;
  font-size: 15px;
  color: $text-dark;
  line-height: 1.5;
}

.options-container {
  padding: 0 16px;
}

.option-item {
  display: flex;
  align-items: center;
  padding: 10px;
  border: 1px solid $border-gray-light;
  border-radius: 6px;
  margin-bottom: 10px;
  background-color: $card-bg;

  &.selected {
    border-color: $primary-blue;
    background-color: $light-blue-bg;
  }
}

.option-indicator {
  width: 28px;
  height: 28px;
  border-radius: 50%;
  border: 1px solid $border-gray-medium;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 10px;
  font-size: 14px;
  color: $text-gray;

  &.selected {
    border-color: $primary-blue;
    background-color: $card-bg;
    color: $primary-blue;
  }
}

.option-text {
  font-size: 14px;
  color: $text-dark;
  flex: 1;
}

.submit-button-container {
  padding: 10px 16px 20px;
}

.submit-button {
  background-color: $button-blue-bg;
  color: $text-white;
  font-size: 15px;
  text-align: center;
  padding: 10px 0;
  border-radius: 7.5px;
}

.explanation-section {
  background-color: $light-blue-bg;
  padding: 15px;
  margin: 0 16px;
  border-radius: 6px;
}

.explanation-title {
  font-size: 15px;
  color: $primary-blue;
  display: block;
  margin-bottom: 10px;
}

.explanation-content, .explanation-detail {
  font-size: 14px;
  color: $text-dark;
  display: block;
  line-height: 1.5;
}
.explanation-content {
  margin-bottom: 5px;
}


.bottom-navigation {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  justify-content: space-around;
  align-items: center;
  background-color: $card-bg;
  padding: 12px 16px;
  padding-bottom: calc(12px + env(safe-area-inset-bottom));
  border-top: 1px solid #f4f4f4;
  height: 60px;
}

.nav-button {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 40px;
  padding: 0 15px;
  border-radius: 20px;
  font-size: 14px;
  min-width: 100px;
  box-sizing: border-box;

  text {
    line-height: 1;
  }
}

.prev-button {
  background-color: $light-blue-bg;
  border: 1px solid $prev-button-border;
  color: $primary-blue;
  .icon-arrow-left {
    margin-right: 5px;
    display: flex;
    align-items: center;
  }
}

.list-button {
  background-color: $list-button-bg;
  border: 1px solid $list-button-border;
  color: $text-gray;
}

.next-button {
  background-color: $button-blue-bg;
  color: $text-white;
  .icon-arrow-right {
    margin-left: 5px;
    display: flex;
    align-items: center;
  }
}

.icon-arrow-left svg,
.icon-arrow-right svg {
  width: 8px;
  height: 14px;
}
</style>
