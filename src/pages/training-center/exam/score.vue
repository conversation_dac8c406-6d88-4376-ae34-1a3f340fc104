<script setup lang="ts">
import { ref } from 'vue'

const score = ref(89)
const totalQuestions = ref(100)
const correctQuestions = ref(89)
const wrongQuestions = ref(11)

const examTime = ref('42分36秒')
const avgTimePerQuestion = ref('25.6秒')

function onViewWrongQuestions() {
  uni.showToast({
    title: '查看错题（功能待实现）',
    icon: 'none',
  })
}

function onComplete() {
  uni.navigateBack()
}
</script>

<template>
  <view class="exam-result-page">
    <view class="score-card">
      <view class="card-title score-card__title">
        考试结果
      </view>
      <view class="score-card__content">
        <view class="success-icon-container">
          <view class="success-icon__outer-circle">
            <view class="success-icon__inner-circle">
              <view class="i-carbon-checkmark text-[#1677FF] text-[24px]" />
            </view>
          </view>
        </view>
        <view class="score-display">
          <text class="score-value">{{ score }}</text>
          <text class="score-unit">分</text>
        </view>
        <text class="congrats-message">恭喜您通过了考试！</text>
        <text class="details-link">查看详细得分情况 ></text>
      </view>
    </view>

    <view class="stats-card">
      <view class="card-title stats-card__title">
        答题统计
      </view>
      <view class="stats-card__content">
        <view class="stat-item">
          <view class="stat-item__circle stat-item__circle--correct">
            <text class="stat-item__value stat-item__value--correct">{{ correctQuestions }}</text>
          </view>
          <text class="stat-item__label">正确题数</text>
        </view>
        <view class="stat-item">
          <view class="stat-item__circle stat-item__circle--wrong">
            <text class="stat-item__value stat-item__value--wrong">{{ wrongQuestions }}</text>
          </view>
          <text class="stat-item__label">错误题数</text>
        </view>
        <view class="stat-item">
          <view class="stat-item__circle stat-item__circle--total">
            <text class="stat-item__value stat-item__value--total">{{ totalQuestions }}</text>
          </view>
          <text class="stat-item__label">总题数</text>
        </view>
      </view>
    </view>

    <view class="duration-card">
      <view class="duration-card__row">
        <text class="duration-card__label">考试用时</text>
        <text class="duration-card__value">{{ examTime }}</text>
      </view>
      <view class="duration-card__row">
        <text class="duration-card__label-avg">平均每题用时：</text>
        <text class="duration-card__value-avg">{{ avgTimePerQuestion }}</text>
      </view>
    </view>

    <view class="bottom-actions">
      <view
        class="action-button action-button--wrong"
        @click="onViewWrongQuestions"
      >
        查看错题
      </view>
      <view
        class="action-button action-button--complete"
        @click="onComplete"
      >
        完成
      </view>
    </view>
  </view>
</template>

<route lang="json">
{
  "style": {
    "navigationBarTitleText": "单片更换培训考试",
    "navigationStyle": "default"
  }
}
</route>

<style scoped lang="scss">
.exam-result-page {
  background-color: #f7f7f5;
  min-height: 100vh;
  padding-bottom: calc(95px + env(safe-area-inset-bottom));
  padding-top: 1px;
  box-sizing: border-box;
}

.card-title {
  font-weight: 500;
}

.score-card {
  background-color: #ffffff;
  margin: 16px;
  border-radius: 8px;
  overflow: hidden;

  &__title {
    background-color: #f0f7ff;
    padding: 11px 16px;
    color: #1677ff;
    font-size: 16px;
  }

  &__content {
    padding: 20px;
    display: flex;
    flex-direction: column;
    align-items: center;
  }
}

.success-icon-container {
  margin-bottom: 15px;
}

.success-icon__outer-circle {
  background-color: #e6f7ff;
  width: 64px;
  height: 64px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.success-icon__inner-circle {
  background-color: #bae7ff;
  width: 48px;
  height: 48px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.score-display {
  margin-bottom: 10px;
  display: flex;
  align-items: flex-end;
}

.score-value {
  font-family: 'PingFang SC', sans-serif;
  color: #1677ff;
  font-size: 24px;
  font-weight: 500;
}

.score-unit {
  font-family: 'PingFang SC', sans-serif;
  color: #666666;
  font-size: 15px;
  margin-left: 5px;
}

.congrats-message {
  font-family: 'PingFang SC', sans-serif;
  color: #666666;
  font-size: 15px;
  margin-bottom: 10px;
}

.details-link {
  font-family: 'PingFang SC', sans-serif;
  color: #1677ff;
  font-size: 14px;
}

.stats-card {
  background-color: #ffffff;
  margin: 16px;
  border-radius: 6px;
  overflow: hidden;

  &__title {
    background-color: #f0f7ff;
    padding: 11px 16px;
    color: #1677ff;
    font-size: 16px;
  }

  &__content {
    padding: 20px;
    display: flex;
    justify-content: space-around;
    align-items: center;
  }
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;

  &__circle {
    width: 52px;
    height: 52px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 10px;

    &--correct {
      background-color: #e6f7ff;
    }
    &--wrong {
      background-color: #fff1f0;
    }
    &--total {
      background-color: #f0f7ff;
    }
  }

  &__value {
    font-family: 'PingFang SC', sans-serif;
    font-size: 18px;
    &--correct {
      color: #1677ff;
    }
    &--wrong {
      color: #f5222d;
    }
    &--total {
      color: #666666;
    }
  }

  &__label {
    font-family: 'PingFang SC', sans-serif;
    color: #666666;
    font-size: 13px;
  }
}

.duration-card {
  background-color: #ffffff;
  margin: 16px;
  padding: 16px;
  border-radius: 8px;

  &__row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    &:not(:last-child) {
      margin-bottom: 10px;
    }
  }

  &__label {
    font-family: 'PingFang SC', sans-serif;
    color: #333333;
    font-size: 15px;
  }

  &__value {
    font-family: 'PingFang SC', sans-serif;
    color: #1677ff;
    font-size: 15px;
  }

  &__label-avg {
    font-family: 'PingFang SC', sans-serif;
    color: #999999;
    font-size: 13px;
  }

  &__value-avg {
    font-family: 'PingFang SC', sans-serif;
    color: #666666;
    font-size: 13px;
  }
}

.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #ffffff;
  padding: 16px;
  padding-top: 12px;
  border-top: 1px solid #f4f4f4;
}

.action-button {
  font-family: 'PingFang SC', sans-serif;
  box-sizing: border-box;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 7.5px;
  font-size: 15px;

  &--wrong {
    margin-bottom: 12px;
    border: 1px dashed #1677ff;
    color: #1677ff;
  }

  &--complete {
    background-color: #37acfe;
    color: #ffffff;
  }
}

</style>
