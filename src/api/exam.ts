import type { PaginatedContent } from '@/service/types'
import type {
  Exam,
  ExamAnswer,
  ExamAnswerDetail,
  ExamAnswerRequest,
  ExamQuery,
  GetAnswerByExamAndUserParams,
  GetAnswerDetailParams,
  GetAnswersByExamParams,
  GetOrCreateAnswerParams,
} from '@/types/api/Exam'
import { getInstance } from '@/service'
import { useUserStore } from '@/store'

/**
 * 分页查询当前用户考试列表
 * @param data - 查询参数
 */
export async function getExamUserPage(data: ExamQuery): Promise<PaginatedContent<Exam>> {
  const userStore = useUserStore()
  const userType = userStore.userInfo?.userType
  if (!userType) {
    return Promise.reject(new Error('未获取到用户类型'))
  }
  const urlMap = {
    haier: '/light/operation/exam/user-page',
    merchant: '',
  }
  const path = urlMap[userType as keyof typeof urlMap]
  if (!path) {
    return Promise.reject(new Error(`当前用户类型 "${userType}" 没有对应的请求路径`))
  }
  return await getInstance().post(path, data)
}

/**
 * 根据考试ID和用户ID获取答卷
 * @param params - 查询参数
 */
export async function getAnswerByExamAndUser(
  params: GetAnswerByExamAndUserParams,
): Promise<ExamAnswer> {
  const userStore = useUserStore()
  const userType = userStore.userInfo?.userType
  const urlMap = {
    haier: '/light/operation/exam/answer/getByExamAndUser',
  }
  const path = urlMap[userType as keyof typeof urlMap]
  return await getInstance().get(path, { params })
}

/**
 * 获取答卷详情
 * @param params - 查询参数
 */
export async function getAnswerDetail(params: GetAnswerDetailParams): Promise<ExamAnswerDetail> {
  const userStore = useUserStore()
  const userType = userStore.userInfo?.userType
  const urlMap = {
    haier: '/light/operation/exam/answer/getDetail',
  }
  const path = urlMap[userType as keyof typeof urlMap]
  return await getInstance().get(path, { params })
}

/**
 * 获取或创建答卷
 * @param params - 查询参数
 */
export async function getOrCreateAnswer(params: GetOrCreateAnswerParams): Promise<ExamAnswer> {
  const userStore = useUserStore()
  const userType = userStore.userInfo?.userType
  const urlMap = {
    haier: '/light/operation/exam/answer/getOrCreate',
  }
  const path = urlMap[userType as keyof typeof urlMap]
  return await getInstance().get(path, { params })
}

/**
 * 根据考试ID获取所有答卷
 * @param params - 查询参数
 */
export async function getAnswersByExam(params: GetAnswersByExamParams): Promise<ExamAnswer[]> {
  const userStore = useUserStore()
  const userType = userStore.userInfo?.userType
  const urlMap = {
    haier: '/light/operation/exam/answer/listByExam',
  }
  const path = urlMap[userType as keyof typeof urlMap]
  return await getInstance().get(path, { params })
}

/**
 * 提交答案
 * @param data - 请求体
 */
export async function submitAnswer(data: ExamAnswerRequest): Promise<boolean> {
  const userStore = useUserStore()
  const userType = userStore.userInfo?.userType
  const urlMap = {
    haier: '/light/operation/exam/answer/submit',
  }
  const path = urlMap[userType as keyof typeof urlMap]
  return await getInstance().post(path, data)
}

/**
 * 更新答题进度
 * @param data - 请求体
 */
export async function updateAnswerProgress(data: ExamAnswerRequest): Promise<boolean> {
  const userStore = useUserStore()
  const userType = userStore.userInfo?.userType
  const urlMap = {
    haier: '/light/operation/exam/answer/updateProgress',
  }
  const path = urlMap[userType as keyof typeof urlMap]
  return await getInstance().post(path, data)
}
