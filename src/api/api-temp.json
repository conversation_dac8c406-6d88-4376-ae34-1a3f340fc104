{"swagger": "2.0", "info": {"description": "外部系统调用网关", "version": "1.0", "title": "日日顺乐农商户平台网关API接口", "contact": {"name": "rrsjk", "url": "http://www.rrsjk.com", "email": "<EMAIL>"}}, "host": "operation.xiaoxianglink.com", "basePath": "/hdsapi", "paths": {"/light/operation/exam/answer/getByExamAndUser": {"get": {"tags": ["光伏运维/考试答卷管理"], "summary": "根据考试ID和用户ID获取答卷", "operationId": "getAnswerByExamIdAndUserIdUsingGET", "produces": ["*/*"], "parameters": [{"name": "Authorization", "in": "header", "description": "访问令牌,格式Bearer ${access_token}", "required": false, "type": "string"}, {"name": "examId", "in": "query", "description": "考试ID", "required": true, "type": "integer", "format": "int64"}, {"name": "userId", "in": "query", "description": "用户ID", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/LightOperationExamAnswer"}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/light/operation/exam/answer/getDetail": {"get": {"tags": ["光伏运维/考试答卷管理"], "summary": "获取答卷详情", "operationId": "getAnswerDetailUsingGET", "produces": ["*/*"], "parameters": [{"name": "Authorization", "in": "header", "description": "访问令牌,格式Bearer ${access_token}", "required": false, "type": "string"}, {"name": "answerId", "in": "query", "description": "答卷ID", "required": true, "type": "integer", "format": "int64"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/LightOperationExamAnswerDto"}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/light/operation/exam/answer/getOrCreate": {"get": {"tags": ["光伏运维/考试答卷管理"], "summary": "获取或创建答卷", "operationId": "getOrCreateAnswerUsingGET", "produces": ["*/*"], "parameters": [{"name": "Authorization", "in": "header", "description": "访问令牌,格式Bearer ${access_token}", "required": false, "type": "string"}, {"name": "examId", "in": "query", "description": "examId", "required": true, "type": "integer", "format": "int64"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/LightOperationExamAnswer"}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/light/operation/exam/answer/listByExam": {"get": {"tags": ["光伏运维/考试答卷管理"], "summary": "根据考试ID获取所有答卷", "operationId": "getAnswersByExamIdUsingGET", "produces": ["*/*"], "parameters": [{"name": "Authorization", "in": "header", "description": "访问令牌,格式Bearer ${access_token}", "required": false, "type": "string"}, {"name": "examId", "in": "query", "description": "考试ID", "required": true, "type": "integer", "format": "int64"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/LightOperationExamAnswer"}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/light/operation/exam/answer/submit": {"post": {"tags": ["光伏运维/考试答卷管理"], "summary": "提交答案", "operationId": "submitAnswerUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "Authorization", "in": "header", "description": "访问令牌,格式Bearer ${access_token}", "required": false, "type": "string"}, {"in": "body", "name": "request", "description": "request", "required": true, "schema": {"$ref": "#/definitions/ExamAnswerRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ExecuteResult«boolean»"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/light/operation/exam/answer/updateProgress": {"post": {"tags": ["光伏运维/考试答卷管理"], "summary": "更新答题进度", "operationId": "updateAnswerProgressUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "Authorization", "in": "header", "description": "访问令牌,格式Bearer ${access_token}", "required": false, "type": "string"}, {"in": "body", "name": "request", "description": "request", "required": true, "schema": {"$ref": "#/definitions/ExamAnswerRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ExecuteResult«boolean»"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}}, "LightOperationExamAnswer": {"type": "object", "properties": {"answerData": {"type": "string", "description": "答题数据JSON"}, "createdAt": {"type": "string", "format": "date-time", "description": "创建时间"}, "createdBy": {"type": "string", "description": "创建人"}, "endTime": {"type": "string", "format": "date-time", "description": "结束答题时间"}, "examId": {"type": "integer", "format": "int64", "description": "考试ID"}, "id": {"type": "integer", "format": "int64", "description": "主键ID"}, "result": {"type": "string", "description": "考试结果：PASS-通过，FAIL-不通过"}, "score": {"type": "integer", "format": "int32", "description": "得分"}, "startTime": {"type": "string", "format": "date-time", "description": "开始答题时间"}, "status": {"type": "string", "description": "状态：NOT_STARTED-待考试，IN_PROGRESS-进行中，COMPLETED-已完成"}, "updatedAt": {"type": "string", "format": "date-time", "description": "更新时间"}, "updatedBy": {"type": "string", "description": "更新人"}, "userId": {"type": "string", "description": "用户ID"}, "userName": {"type": "string", "description": "用户名称"}}, "title": "LightOperationExamAnswer", "description": "考试答卷实体"}, "LightOperationExamAnswerDto": {"type": "object", "properties": {"answers": {"type": "object", "description": "答案数据", "additionalProperties": {"type": "string"}}, "duration": {"type": "integer", "format": "int32", "description": "考试时长（分钟）"}, "endTime": {"type": "string", "format": "date-time", "description": "结束时间"}, "examId": {"type": "integer", "format": "int64", "description": "考试ID"}, "examName": {"type": "string", "description": "考试名称"}, "id": {"type": "integer", "format": "int64", "description": "答卷ID"}, "passScore": {"type": "integer", "format": "int32", "description": "及格分数"}, "questions": {"type": "array", "description": "试题列表", "items": {"$ref": "#/definitions/QuestionVO"}}, "result": {"type": "integer", "format": "int32", "description": "考试结果：0-不通过，1-通过"}, "score": {"type": "integer", "format": "int32", "description": "得分"}, "startTime": {"type": "string", "format": "date-time", "description": "开始时间"}, "status": {"type": "integer", "format": "int32", "description": "状态：0-未开始，1-进行中，2-已完成"}, "totalScore": {"type": "integer", "format": "int32", "description": "总分"}, "userId": {"type": "string", "description": "用户ID"}, "userName": {"type": "string", "description": "用户姓名"}}, "title": "LightOperationExamAnswerDto", "description": "考试答卷DTO"}, "LightOperationExamBankRel": {"type": "object", "properties": {"bankId": {"type": "integer", "format": "int64"}, "bankName": {"type": "string"}, "examId": {"type": "integer", "format": "int64"}, "extractCount": {"type": "integer", "format": "int32"}, "id": {"type": "integer", "format": "int64"}, "multipleChoiceCount": {"type": "integer", "format": "int32"}, "singleChoiceCount": {"type": "integer", "format": "int32"}}, "title": "LightOperationExamBankRel"}, "LightOperationExamPaper": {"type": "object", "properties": {"createdAt": {"type": "string", "format": "date-time", "description": "创建时间"}, "createdBy": {"type": "string", "description": "创建人"}, "examId": {"type": "integer", "format": "int64", "description": "考试ID"}, "id": {"type": "integer", "format": "int64", "description": "主键ID"}, "multipleChoiceCount": {"type": "integer", "format": "int32", "description": "多选题数量"}, "questionData": {"type": "string", "description": "试题数据JSON"}, "singleChoiceCount": {"type": "integer", "format": "int32", "description": "单选题数量"}, "totalCount": {"type": "integer", "format": "int32", "description": "总题数"}}, "title": "LightOperationExamPaper", "description": "考试标准试卷实体"}, "ExamAnswerRequest": {"type": "object", "required": ["answerData", "id"], "properties": {"answerData": {"type": "object", "example": {"1": "A", "2": "A,C,D", "3": "正确答案文本"}, "description": "答案数据", "additionalProperties": {"type": "string"}}, "id": {"type": "integer", "format": "int64", "example": 1, "description": "答卷ID"}}, "title": "ExamAnswerRequest", "description": "答卷操作请求DTO"}}