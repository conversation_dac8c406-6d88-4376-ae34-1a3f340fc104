/**
 * 考试查询参数
 */
export interface ExamQuery {
  /**
   * 创建人
   */
  createdBy?: string
  /**
   * 当前时间（用于查询进行中的考试）
   */
  currentTime?: string
  /**
   * 可见考试ID列表（用于权限过滤）
   */
  examIds?: number[]
  /**
   * 考试名称
   */
  name?: string
  /**
   * 页码
   */
  pageNum?: number
  /**
   * 一页数据量
   */
  pageSize?: number
  /**
   * 考试状态
   */
  status?: string
  /**
   * 用户答卷状态（NOT_STARTED-未开始，IN_PROGRESS-进行中，COMPLETED-已完成）
   */
  userExamStatus?: string
  /**
   * 用户ID（用于查询用户可见的考试）
   */
  userId?: string
  /**
   * 用户角色列表（用于查询用户可见的考试）
   */
  userRoles?: string[]
  /**
   * 用户分中心编码列表（用于查询用户可见的考试）
   */
  userSubCenterCodes?: string[]
}

/**
 * 考试信息
 */
export interface Exam {
  /**
   * 答卷ID
   */
  answerId?: number
  /**
   * 考试 ID
   */
  id: number
  /**
   * 考试名称
   */
  name: string
  /**
   * 考试状态
   */
  status: string
  /**
   * 用户考试状态
   */
  userExamStatus: string
  /**
   * 开始时间
   */
  startTime: string
  /**
   * 结束时间
   */
  endTime: string
  /**
   * 考试时长（分钟）
   */
  duration: number
  /**
   * 总分
   */
  totalScore: number
  /**
   * 及格分
   */
  passScore: number
  /**
   * 用户得分
   */
  userScore: number | null
}

/**
 * 考试答卷实体
 */
export interface ExamAnswer {
  /**
   * 答题数据JSON
   */
  answerData: string
  /**
   * 创建时间
   */
  createdAt: string
  /**
   * 创建人
   */
  createdBy: string
  /**
   * 结束答题时间
   */
  endTime: string
  /**
   * 考试ID
   */
  examId: number
  /**
   * 主键ID
   */
  id: number
  /**
   * 考试结果：PASS-通过，FAIL-不通过
   */
  result: string
  /**
   * 得分
   */
  score: number
  /**
   * 开始答题时间
   */
  startTime: string
  /**
   * 状态：NOT_STARTED-待考试，IN_PROGRESS-进行中，COMPLETED-已完成
   */
  status: string
  /**
   * 更新时间
   */
  updatedAt: string
  /**
   * 更新人
   */
  updatedBy: string
  /**
   * 用户ID
   */
  userId: string
  /**
   * 用户名称
   */
  userName: string
}

/**
 * 题目选项
 */
export interface LightOperationQuestionOption {
  /**
   * 选项内容
   */
  content?: string
  /**
   * 主键ID
   */
  id?: number
  /**
   * 选项标识(A,B,C,D等)
   */
  optionKey?: string
  /**
   * 排序号
   */
  sortNo?: number
}

/**
 * 题目展示对象
 */
export interface QuestionVO {
  /**
   * 解析
   */
  analysis?: string
  /**
   * 正确答案
   */
  answer?: string
  /**
   * 题库ID
   */
  bankId?: number
  /**
   * 是否答对
   */
  correct?: boolean
  /**
   * 难度：1-简单，2-一般，3-困难
   */
  difficulty?: number
  /**
   * 主键ID
   */
  id?: number
  /**
   * 选项列表
   */
  optionList?: LightOperationQuestionOption[]
  /**
   * 分值
   */
  score?: number
  /**
   * 题目标题
   */
  title?: string
  /**
   * 试题类型：SINGLE-单选题，MULTIPLE-多选题
   */
  type?: string
  /**
   * 用户答案
   */
  userAnswer?: string
}

/**
 * 考试答卷详情
 */
export interface ExamAnswerDetail {
  /**
   * 答案数据
   */
  answers: Record<string, string>
  /**
   * 考试时长（分钟）
   */
  duration: number
  /**
   * 结束时间
   */
  endTime: string
  /**
   * 考试ID
   */
  examId: number
  /**
   * 考试名称
   */
  examName: string
  /**
   * 答卷ID
   */
  id: number
  /**
   * 及格分数
   */
  passScore: number
  /**
   * 试题列表
   */
  questions: QuestionVO[]
  /**
   * 考试结果：0-不通过，1-通过
   */
  result: number
  /**
   * 得分
   */
  score: number
  /**
   * 开始时间
   */
  startTime: string
  /**
   * 状态：0-未开始，1-进行中，2-已完成
   */
  status: number
  /**
   * 总分
   */
  totalScore: number
  /**
   * 用户ID
   */
  userId: string
  /**
   * 用户姓名
   */
  userName: string
}

/**
 * 答卷操作请求
 */
export interface ExamAnswerRequest {
  /**
   * 答案数据
   */
  answerData: Record<string, string>
  /**
   * 答卷ID
   */
  id: number
}

/**
 * 根据考试ID和用户ID获取答卷的参数
 */
export interface GetAnswerByExamAndUserParams {
  /**
   * 考试ID
   */
  examId: number
  /**
   * 用户ID
   */
  userId: string
}

/**
 * 获取答卷详情的参数
 */
export interface GetAnswerDetailParams {
  /**
   * 答卷ID
   */
  answerId: number
}

/**
 * 获取或创建答卷的参数
 */
export interface GetOrCreateAnswerParams {
  /**
   * examId
   */
  examId: number
}

/**
 * 根据考试ID获取所有答卷的参数
 */
export interface GetAnswersByExamParams {
  /**
   * 考试ID
   */
  examId: number
}
