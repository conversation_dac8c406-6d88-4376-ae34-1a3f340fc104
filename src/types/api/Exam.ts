/**
 * 分页查询考试列表的查询参数
 */
export interface ExamQuery {
  pageNum: number
  pageSize: number
  name?: string
  status?: string
  userExamStatus?: string
}

/**
 * 根据考试ID和用户ID获取答卷的查询参数
 */
export interface GetAnswerByExamAndUserParams {
  examId: number
  userId: string
}

/**
 * 获取答卷详情的查询参数
 */
export interface GetAnswerDetailParams {
  answerId: number
}

/**
 * 获取或创建答卷的查询参数
 */
export interface GetOrCreateAnswerParams {
  examId: number
}

/**
 * 根据考试ID获取所有答卷的查询参数
 */
export interface GetAnswersByExamParams {
  examId: number
}

/**
 * 考试问题选项
 */
export interface ExamQuestionOption {
  id: number
  content: string
  optionKey: string
  isCorrect?: boolean
  isSelected?: boolean
}

/**
 * 考试问题
 */
export interface ExamQuestion {
  id: number
  type: string
  title: string
  content?: string
  score?: number
  answer?: string
  analysis?: string
  options: ExamQuestionOption[]
}

/**
 * 考试答卷详情
 */
export interface ExamAnswerDetail {
  id: number
  examId: number
  examName: string
  startTime: string
  endTime?: string
  duration: number
  status: number
  answers: Record<string, any>
  questions: ExamQuestion[]
}

/**
 * 考试答卷
 */
export interface ExamAnswer {
  id: number
  examId: number
  userId: string
  status: string
  score: number
  startTime: string
  endTime:string
  progress: number
}

/**
 * 提交答案的请求体
 */
export interface ExamAnswerRequest {
  id: number
  answerData: Record<string, string>
}

export interface LightOperationExamBankRel {
  [key: string]: any
}

export interface Exam {
  answerId?: number
  bankRels?: LightOperationExamBankRel[]
  createdAt?: string
  createdBy?: string
  delFlag?: number
  duration?: number
  endTime?: string
  id?: number
  name?: string
  passScore?: number
  questionCount?: number
  roleCodes?: string[]
  startTime?: string
  status?: string
  subCenterCodes?: string[]
  totalScore?: number
  updatedAt?: string
  updatedBy?: string
  userExamStatus?: string
  userScore?: number
}

export interface ExamPageQuery {
  createdBy?: string
  currentTime?: string
  examIds?: number[]
  name?: string
  pageNum?: number
  pageSize?: number
  status?: string
  userExamStatus?: string
  userId?: string
  userRoles?: string[]
  userSubCenterCodes?: string[]
}
